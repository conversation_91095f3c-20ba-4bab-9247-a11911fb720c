#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的AI一致性测试脚本
用于快速验证基本功能
"""

import openai
import json
import os
from datetime import datetime
from config import API_CONFIG, MODEL_CONFIG, SYSTEM_PROMPT

def test_simple_consistency():
    """简单的一致性测试"""
    print("开始简单一致性测试...")
    
    # 检查temperature设置
    print(f"当前temperature设置: {MODEL_CONFIG['temperature']}")
    if MODEL_CONFIG['temperature'] != 0.0:
        print("警告: temperature不为0.0，可能影响一致性")
    
    # 初始化客户端
    client = openai.OpenAI(
        base_url=API_CONFIG["base_url"],
        api_key=API_CONFIG["api_key"]
    )
    
    # 简单测试用例
    test_input = """
【业务类别】: 票据
【客户问题】: 如何查询我的票据状态？
【客服回答】: 您可以登录微众银行APP，在票据业务页面查看您的票据状态。
【业务分类】: 票据查询

请根据银行客服合规标准，对上述问答对进行全面的合规性评估。
    """.strip()
    
    # 执行3次测试
    outputs = []
    for i in range(3):
        print(f"执行第 {i+1} 次测试...")
        
        try:
            messages = [
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": test_input}
            ]
            
            response = client.chat.completions.create(
                model=API_CONFIG["model"],
                messages=messages,
                temperature=MODEL_CONFIG["temperature"],
                max_tokens=MODEL_CONFIG["max_tokens"]
            )
            
            output = response.choices[0].message.content
            outputs.append(output)
            
            # 保存输出
            with open(f"test_output_{i+1}.txt", 'w', encoding='utf-8') as f:
                f.write(f"测试轮次: {i+1}\n")
                f.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 50 + "\n")
                f.write(output)
            
            print(f"第 {i+1} 次测试完成，输出长度: {len(output)} 字符")
            
        except Exception as e:
            print(f"第 {i+1} 次测试失败: {e}")
            return
    
    # 比较输出
    print("\n比较结果:")
    if len(set(outputs)) == 1:
        print("✓ 所有输出完全一致!")
    else:
        print("✗ 输出不一致!")
        for i, output in enumerate(outputs, 1):
            print(f"输出 {i} 长度: {len(output)} 字符")
        
        # 保存比较结果
        with open("consistency_comparison.txt", 'w', encoding='utf-8') as f:
            f.write("一致性比较结果\n")
            f.write("=" * 50 + "\n\n")
            
            for i in range(len(outputs)):
                for j in range(i+1, len(outputs)):
                    f.write(f"输出 {i+1} vs 输出 {j+1}:\n")
                    if outputs[i] == outputs[j]:
                        f.write("完全一致\n\n")
                    else:
                        f.write("存在差异\n")
                        f.write(f"长度差异: {len(outputs[i]) - len(outputs[j])} 字符\n\n")
    
    print("测试完成，详细输出已保存到 test_output_*.txt 文件")

if __name__ == "__main__":
    test_simple_consistency()
