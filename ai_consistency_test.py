#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI模型输出一致性验证脚本
验证当temperature=0.0时，相同输入是否产生完全一致的输出
"""

import openai
import json
import os
import time
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Tuple
import logging
from config import API_CONFIG, MODEL_CONFIG, SYSTEM_PROMPT

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ai_consistency_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class AIConsistencyTester:
    """AI模型一致性测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.client = openai.OpenAI(
            base_url=API_CONFIG["base_url"],
            api_key=API_CONFIG["api_key"]
        )
        self.model = API_CONFIG["model"]
        self.system_prompt = SYSTEM_PROMPT
        
        # 创建测试结果目录
        self.test_dir = "consistency_test_results"
        os.makedirs(self.test_dir, exist_ok=True)
        
        # 测试配置
        self.test_rounds = 5  # 每个测试用例重复5次
        self.request_delay = 2  # 请求间隔（秒）
        
    def check_temperature_setting(self) -> bool:
        """检查temperature设置是否为0.0"""
        current_temp = MODEL_CONFIG.get("temperature", None)
        if current_temp != 0.0:
            logging.error(f"Temperature设置不正确: {current_temp}, 应该为0.0")
            return False
        logging.info(f"Temperature设置正确: {current_temp}")
        return True
    
    def get_test_cases(self) -> List[Dict[str, Any]]:
        """获取测试用例"""
        test_cases = [
            {
                "name": "简单问题测试",
                "description": "测试简单的业务咨询问题",
                "input": {
                    "类别": "票据",
                    "问题": "如何查询我的票据状态？",
                    "回答": "您可以登录微众银行APP，在票据业务页面查看您的票据状态。",
                    "分类": "票据查询"
                }
            },
            {
                "name": "复杂问题测试", 
                "description": "测试复杂的合规评估场景",
                "input": {
                    "类别": "票据",
                    "问题": "如果我的企业是首次贴现，贴现息会比较低吗？",
                    "回答": "首次贴现的企业，我行会根据企业的资质情况进行评估，贴现利率会根据市场情况和企业信用等级确定。建议您联系我行客户经理了解具体的贴现利率。",
                    "分类": "票据贴现"
                }
            },
            {
                "name": "边界情况测试",
                "description": "测试可能存在歧义的回答",
                "input": {
                    "类别": "票据", 
                    "问题": "如果我现在不打算使用这个优惠券，会有什么后果？",
                    "回答": "如果优惠券到期未使用，可能会错过此次优惠机会。建议如果有需要结算类业务，尽早联系我行了解具体情况",
                    "分类": None
                }
            }
        ]
        return test_cases
    
    def format_qa_for_evaluation(self, qa_data: Dict[str, Any]) -> str:
        """格式化问答对用于评估（与主程序保持一致）"""
        category = qa_data.get("类别", "未知")
        question = qa_data.get("问题", "")
        answer = qa_data.get("回答", "")
        classification = qa_data.get("分类", "")
        
        evaluation_text = f"""
【业务类别】: {category}
【客户问题】: {question}
【客服回答】: {answer}
【业务分类】: {classification if classification else "未分类"}

请根据银行客服合规标准，对上述问答对进行全面的合规性评估。
        """.strip()
        
        return evaluation_text
    
    def call_ai_model(self, input_text: str) -> str:
        """调用AI模型"""
        try:
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": input_text}
            ]
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=MODEL_CONFIG["temperature"],
                max_tokens=MODEL_CONFIG["max_tokens"]
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logging.error(f"AI模型调用失败: {e}")
            return f"ERROR: {str(e)}"
    
    def calculate_hash(self, text: str) -> str:
        """计算文本的MD5哈希值"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def test_single_case(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """测试单个用例"""
        case_name = test_case["name"]
        case_description = test_case["description"]
        input_data = test_case["input"]
        
        logging.info(f"开始测试用例: {case_name}")
        
        # 格式化输入
        formatted_input = self.format_qa_for_evaluation(input_data)
        
        # 存储所有输出
        outputs = []
        hashes = []
        
        # 执行多轮测试
        for round_num in range(1, self.test_rounds + 1):
            logging.info(f"  执行第 {round_num} 轮测试...")
            
            # 调用AI模型
            output = self.call_ai_model(formatted_input)
            
            # 计算哈希值
            output_hash = self.calculate_hash(output)
            
            # 保存结果
            round_result = {
                "round": round_num,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "output": output,
                "hash": output_hash,
                "length": len(output)
            }
            
            outputs.append(round_result)
            hashes.append(output_hash)
            
            # 保存单轮输出到文件
            output_filename = f"{case_name.replace(' ', '_')}_round_{round_num:02d}.txt"
            output_filepath = os.path.join(self.test_dir, output_filename)
            with open(output_filepath, 'w', encoding='utf-8') as f:
                f.write(f"测试用例: {case_name}\n")
                f.write(f"轮次: {round_num}\n")
                f.write(f"时间: {round_result['timestamp']}\n")
                f.write(f"哈希值: {output_hash}\n")
                f.write("=" * 80 + "\n")
                f.write(output)
            
            # 添加延迟
            if round_num < self.test_rounds:
                time.sleep(self.request_delay)
        
        # 分析一致性
        unique_hashes = set(hashes)
        is_consistent = len(unique_hashes) == 1
        
        # 构建测试结果
        test_result = {
            "test_case": case_name,
            "description": case_description,
            "input_data": input_data,
            "formatted_input": formatted_input,
            "total_rounds": self.test_rounds,
            "outputs": outputs,
            "consistency_analysis": {
                "is_consistent": is_consistent,
                "unique_outputs": len(unique_hashes),
                "hash_distribution": {h: hashes.count(h) for h in unique_hashes}
            },
            "test_timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        logging.info(f"  测试用例 {case_name} 完成，一致性: {'通过' if is_consistent else '失败'}")
        
        return test_result
    
    def analyze_differences(self, outputs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分析输出差异"""
        differences = []
        
        if len(outputs) < 2:
            return differences
        
        # 比较每对输出
        for i in range(len(outputs)):
            for j in range(i + 1, len(outputs)):
                output1 = outputs[i]["output"]
                output2 = outputs[j]["output"]
                
                if output1 != output2:
                    # 找出具体差异
                    lines1 = output1.split('\n')
                    lines2 = output2.split('\n')
                    
                    diff_lines = []
                    max_lines = max(len(lines1), len(lines2))
                    
                    for line_num in range(max_lines):
                        line1 = lines1[line_num] if line_num < len(lines1) else ""
                        line2 = lines2[line_num] if line_num < len(lines2) else ""
                        
                        if line1 != line2:
                            diff_lines.append({
                                "line_number": line_num + 1,
                                "round1_content": line1,
                                "round2_content": line2
                            })
                    
                    differences.append({
                        "round1": outputs[i]["round"],
                        "round2": outputs[j]["round"],
                        "different_lines": diff_lines,
                        "length_diff": len(output1) - len(output2)
                    })

        return differences

    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试用例"""
        # 检查temperature设置
        if not self.check_temperature_setting():
            return {"error": "Temperature设置不正确，测试终止"}

        logging.info("开始AI模型一致性测试...")
        logging.info(f"测试配置: 每个用例重复{self.test_rounds}次，请求间隔{self.request_delay}秒")

        # 获取测试用例
        test_cases = self.get_test_cases()

        # 执行所有测试
        all_results = []
        for i, test_case in enumerate(test_cases, 1):
            logging.info(f"执行测试用例 {i}/{len(test_cases)}")
            result = self.test_single_case(test_case)
            all_results.append(result)

            # 如果不是最后一个测试用例，添加延迟
            if i < len(test_cases):
                time.sleep(self.request_delay)

        # 生成汇总报告
        summary = self.generate_summary_report(all_results)

        # 保存完整结果
        complete_results = {
            "test_metadata": {
                "test_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "model": self.model,
                "temperature": MODEL_CONFIG["temperature"],
                "max_tokens": MODEL_CONFIG["max_tokens"],
                "test_rounds_per_case": self.test_rounds,
                "total_test_cases": len(test_cases)
            },
            "test_results": all_results,
            "summary": summary
        }

        # 保存到文件
        results_file = os.path.join(self.test_dir, f"consistency_test_complete_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(complete_results, f, ensure_ascii=False, indent=2)

        logging.info(f"完整测试结果已保存到: {results_file}")

        return complete_results

    def generate_summary_report(self, test_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成汇总报告"""
        total_tests = len(test_results)
        consistent_tests = sum(1 for r in test_results if r["consistency_analysis"]["is_consistent"])
        inconsistent_tests = total_tests - consistent_tests

        # 统计不一致的详细信息
        inconsistent_details = []
        for result in test_results:
            if not result["consistency_analysis"]["is_consistent"]:
                # 分析差异
                differences = self.analyze_differences(result["outputs"])
                inconsistent_details.append({
                    "test_case": result["test_case"],
                    "unique_outputs": result["consistency_analysis"]["unique_outputs"],
                    "hash_distribution": result["consistency_analysis"]["hash_distribution"],
                    "differences": differences
                })

        summary = {
            "overall_consistency": {
                "total_test_cases": total_tests,
                "consistent_cases": consistent_tests,
                "inconsistent_cases": inconsistent_tests,
                "consistency_rate": (consistent_tests / total_tests * 100) if total_tests > 0 else 0
            },
            "inconsistent_details": inconsistent_details,
            "model_info": {
                "model": self.model,
                "temperature": MODEL_CONFIG["temperature"],
                "max_tokens": MODEL_CONFIG["max_tokens"]
            }
        }

        # 生成文本报告
        self.generate_text_report(summary)

        return summary

    def generate_text_report(self, summary: Dict[str, Any]):
        """生成文本格式的报告"""
        report_file = os.path.join(self.test_dir, f"consistency_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("AI模型输出一致性测试报告\n")
            f.write("=" * 80 + "\n\n")

            # 测试概览
            f.write("测试概览:\n")
            f.write("-" * 40 + "\n")
            f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"模型: {summary['model_info']['model']}\n")
            f.write(f"Temperature: {summary['model_info']['temperature']}\n")
            f.write(f"Max Tokens: {summary['model_info']['max_tokens']}\n")
            f.write(f"每个用例测试轮次: {self.test_rounds}\n\n")

            # 一致性结果
            overall = summary["overall_consistency"]
            f.write("一致性测试结果:\n")
            f.write("-" * 40 + "\n")
            f.write(f"总测试用例数: {overall['total_test_cases']}\n")
            f.write(f"一致性通过: {overall['consistent_cases']}\n")
            f.write(f"一致性失败: {overall['inconsistent_cases']}\n")
            f.write(f"一致性率: {overall['consistency_rate']:.1f}%\n\n")

            # 不一致详情
            if summary["inconsistent_details"]:
                f.write("不一致测试用例详情:\n")
                f.write("-" * 40 + "\n")
                for detail in summary["inconsistent_details"]:
                    f.write(f"\n测试用例: {detail['test_case']}\n")
                    f.write(f"不同输出数量: {detail['unique_outputs']}\n")
                    f.write(f"哈希分布: {detail['hash_distribution']}\n")

                    if detail['differences']:
                        f.write("差异分析:\n")
                        for diff in detail['differences']:
                            f.write(f"  轮次 {diff['round1']} vs 轮次 {diff['round2']}:\n")
                            f.write(f"    长度差异: {diff['length_diff']} 字符\n")
                            f.write(f"    不同行数: {len(diff['different_lines'])}\n")

                            # 显示前几个不同的行
                            for i, line_diff in enumerate(diff['different_lines'][:3]):
                                f.write(f"    行 {line_diff['line_number']}:\n")
                                f.write(f"      轮次{diff['round1']}: {line_diff['round1_content'][:100]}...\n")
                                f.write(f"      轮次{diff['round2']}: {line_diff['round2_content'][:100]}...\n")

                            if len(diff['different_lines']) > 3:
                                f.write(f"    ... 还有 {len(diff['different_lines']) - 3} 行不同\n")
            else:
                f.write("所有测试用例都通过了一致性检查！\n")

            f.write("\n" + "=" * 80 + "\n")
            f.write("详细的输出文件请查看对应的 round_XX.txt 文件\n")
            f.write("完整的JSON结果请查看 consistency_test_complete_*.json 文件\n")

        logging.info(f"文本报告已生成: {report_file}")


def main():
    """主函数"""
    print("AI模型输出一致性测试")
    print("=" * 50)

    # 创建测试器
    tester = AIConsistencyTester()

    # 运行测试
    results = tester.run_all_tests()

    if "error" in results:
        print(f"测试失败: {results['error']}")
        return

    # 显示简要结果
    summary = results["summary"]["overall_consistency"]
    print(f"\n测试完成!")
    print(f"总测试用例: {summary['total_test_cases']}")
    print(f"一致性通过: {summary['consistent_cases']}")
    print(f"一致性失败: {summary['inconsistent_cases']}")
    print(f"一致性率: {summary['consistency_rate']:.1f}%")

    if summary['inconsistent_cases'] > 0:
        print(f"\n警告: 发现 {summary['inconsistent_cases']} 个测试用例输出不一致!")
        print("请查看详细报告了解具体差异。")
    else:
        print("\n✓ 所有测试用例都通过了一致性检查!")

    print(f"\n详细结果已保存到: {tester.test_dir}")


if __name__ == "__main__":
    main()
