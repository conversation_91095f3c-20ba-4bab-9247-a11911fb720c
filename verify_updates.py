#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Q&A对文件更新的脚本
检查Excel文件和JSON文件的一致性
"""

import pandas as pd
import json
import os

def verify_updates():
    """验证更新结果"""
    excel_path = "审核问答对.xlsx"
    qa_folder = "问答对"
    
    # 读取Excel文件
    df = pd.read_excel(excel_path)
    print(f"Excel文件包含 {len(df)} 行数据")
    
    # 验证每个JSON文件
    verified_count = 0
    error_count = 0
    
    for index, row in df.iterrows():
        file_number = index + 1
        json_filename = f"{file_number:03d}.json"
        json_path = os.path.join(qa_folder, json_filename)
        
        try:
            # 读取JSON文件
            with open(json_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            # 验证关键字段
            excel_question = str(row['问题']).strip() if pd.notna(row['问题']) else ""
            json_question = json_data.get('问题', "").strip()
            
            if excel_question == json_question:
                verified_count += 1
            else:
                print(f"文件 {json_filename} 问题不匹配:")
                print(f"  Excel: {excel_question}")
                print(f"  JSON:  {json_question}")
                error_count += 1
                
        except Exception as e:
            print(f"验证文件 {json_filename} 时出错: {e}")
            error_count += 1
    
    print(f"\n验证完成!")
    print(f"验证通过: {verified_count} 个文件")
    print(f"验证失败: {error_count} 个文件")
    
    # 显示一些统计信息
    print(f"\n统计信息:")
    
    # 统计可用性
    usable_count = 0
    unusable_count = 0
    
    for i in range(1, 102):
        json_filename = f"{i:03d}.json"
        json_path = os.path.join(qa_folder, json_filename)
        
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            if json_data.get('是否可用') == '可用':
                usable_count += 1
            else:
                unusable_count += 1
                
        except Exception as e:
            print(f"读取文件 {json_filename} 时出错: {e}")
    
    print(f"可用的问答对: {usable_count}")
    print(f"不可用的问答对: {unusable_count}")
    
    # 统计分类
    categories = {}
    for i in range(1, 102):
        json_filename = f"{i:03d}.json"
        json_path = os.path.join(qa_folder, json_filename)
        
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            category = json_data.get('分类')
            if category:
                categories[category] = categories.get(category, 0) + 1
                
        except Exception as e:
            print(f"读取文件 {json_filename} 时出错: {e}")
    
    print(f"\n分类统计:")
    for category, count in sorted(categories.items()):
        print(f"  {category}: {count}")

if __name__ == "__main__":
    verify_updates()
