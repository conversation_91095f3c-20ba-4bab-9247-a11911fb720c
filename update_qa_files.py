#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新Q&A对文件的脚本
根据已更正的Excel文件更新问答对文件夹中的各个JSON文件
"""

import pandas as pd
import json
import os
from pathlib import Path

def read_excel_file(excel_path):
    """读取Excel文件并返回DataFrame"""
    try:
        # 尝试读取Excel文件
        df = pd.read_excel(excel_path)
        print(f"成功读取Excel文件: {excel_path}")
        print(f"数据行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

def update_json_file(json_path, row_data):
    """更新单个JSON文件"""
    try:
        # 读取现有JSON文件
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 更新数据 - 根据Excel列名映射到JSON字段
        # 需要根据实际的Excel列名进行调整
        if '类别' in row_data and pd.notna(row_data['类别']):
            data['类别'] = str(row_data['类别']).strip()
        
        if '问题' in row_data and pd.notna(row_data['问题']):
            data['问题'] = str(row_data['问题']).strip()
        
        if '回答' in row_data and pd.notna(row_data['回答']):
            data['回答'] = str(row_data['回答']).strip()
        
        if '分类' in row_data and pd.notna(row_data['分类']):
            data['分类'] = str(row_data['分类']).strip()
        elif '分类' in row_data and pd.isna(row_data['分类']):
            data['分类'] = None
        
        if '优化后的回答' in row_data and pd.notna(row_data['优化后的回答']):
            data['优化后的回答'] = str(row_data['优化后的回答']).strip()
        elif '优化后的回答' in row_data and pd.isna(row_data['优化后的回答']):
            data['优化后的回答'] = None
        
        if '是否可用' in row_data and pd.notna(row_data['是否可用']):
            data['是否可用'] = str(row_data['是否可用']).strip()
        
        if '原因' in row_data and pd.notna(row_data['原因']):
            data['原因'] = str(row_data['原因']).strip()
        elif '原因' in row_data and pd.isna(row_data['原因']):
            data['原因'] = None
        
        # 写回JSON文件
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        return True
    except Exception as e:
        print(f"更新文件 {json_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    # 文件路径
    excel_path = "审核问答对.xlsx"
    qa_folder = "问答对"
    
    # 检查文件和文件夹是否存在
    if not os.path.exists(excel_path):
        print(f"Excel文件不存在: {excel_path}")
        return
    
    if not os.path.exists(qa_folder):
        print(f"Q&A文件夹不存在: {qa_folder}")
        return
    
    # 读取Excel文件
    df = read_excel_file(excel_path)
    if df is None:
        return
    
    # 显示Excel文件的前几行以便检查
    print("\nExcel文件前5行数据:")
    print(df.head())
    
    # 更新JSON文件
    updated_count = 0
    error_count = 0
    
    for index, row in df.iterrows():
        # 构造JSON文件名 (假设有一个序号列或者使用行索引)
        file_number = index + 1  # 从1开始编号
        json_filename = f"{file_number:03d}.json"  # 格式化为001.json, 002.json等
        json_path = os.path.join(qa_folder, json_filename)
        
        # 检查JSON文件是否存在
        if not os.path.exists(json_path):
            print(f"JSON文件不存在: {json_path}")
            error_count += 1
            continue
        
        # 更新JSON文件
        if update_json_file(json_path, row):
            updated_count += 1
            print(f"已更新: {json_filename}")
        else:
            error_count += 1
    
    print(f"\n更新完成!")
    print(f"成功更新: {updated_count} 个文件")
    print(f"更新失败: {error_count} 个文件")

if __name__ == "__main__":
    main()
