2025-07-21 19:54:46,359 - INFO - Temperature设置正确: 0.0
2025-07-21 19:54:46,359 - INFO - 开始AI模型一致性测试...
2025-07-21 19:54:46,359 - INFO - 测试配置: 每个用例重复5次，请求间隔2秒
2025-07-21 19:54:46,359 - INFO - 执行测试用例 1/3
2025-07-21 19:54:46,359 - INFO - 开始测试用例: 简单问题测试
2025-07-21 19:54:46,360 - INFO -   执行第 1 轮测试...
2025-07-21 19:55:16,118 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 19:55:18,132 - INFO -   执行第 2 轮测试...
2025-07-21 19:55:44,178 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 19:55:46,195 - INFO -   执行第 3 轮测试...
2025-07-21 19:55:59,054 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 19:56:01,057 - INFO -   执行第 4 轮测试...
2025-07-21 19:56:27,264 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 19:56:29,276 - INFO -   执行第 5 轮测试...
2025-07-21 19:57:06,067 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 19:57:06,072 - INFO -   测试用例 简单问题测试 完成，一致性: 失败
2025-07-21 19:57:08,083 - INFO - 执行测试用例 2/3
2025-07-21 19:57:08,084 - INFO - 开始测试用例: 复杂问题测试
2025-07-21 19:57:08,084 - INFO -   执行第 1 轮测试...
2025-07-21 19:57:44,512 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 19:57:46,522 - INFO -   执行第 2 轮测试...
2025-07-21 19:58:09,527 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 19:58:11,533 - INFO -   执行第 3 轮测试...
2025-07-21 19:58:42,843 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 19:58:44,854 - INFO -   执行第 4 轮测试...
2025-07-21 19:59:14,202 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 19:59:16,213 - INFO -   执行第 5 轮测试...
2025-07-21 19:59:43,210 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 19:59:43,216 - INFO -   测试用例 复杂问题测试 完成，一致性: 失败
2025-07-21 19:59:45,230 - INFO - 执行测试用例 3/3
2025-07-21 19:59:45,230 - INFO - 开始测试用例: 边界情况测试
2025-07-21 19:59:45,232 - INFO -   执行第 1 轮测试...
2025-07-21 20:00:15,540 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 20:00:17,556 - INFO -   执行第 2 轮测试...
2025-07-21 20:00:41,870 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 20:00:43,880 - INFO -   执行第 3 轮测试...
2025-07-21 20:01:16,553 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 20:01:18,566 - INFO -   执行第 4 轮测试...
2025-07-21 20:01:58,077 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 20:02:00,088 - INFO -   执行第 5 轮测试...
2025-07-21 20:02:24,191 - INFO - HTTP Request: POST https://api.qingyuntop.top/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 20:02:24,195 - INFO -   测试用例 边界情况测试 完成，一致性: 失败
2025-07-21 20:02:24,200 - INFO - 文本报告已生成: consistency_test_results\consistency_report_20250721_200224.txt
2025-07-21 20:02:24,220 - INFO - 完整测试结果已保存到: consistency_test_results\consistency_test_complete_20250721_200224.json
