import json
import os
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import glob
import re

def clean_text(text):
    """清理文本，移除多余的换行符和空格"""
    if not text:
        return ""
    # 移除多余的换行符和空格
    text = re.sub(r'\n+', '\n', str(text))
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def extract_compliance_result(compliance_text):
    """从合规评估结果中提取关键信息"""
    if not compliance_text:
        return "未评估"

    # 提取最终合规判断
    if "最终合规判断：" in compliance_text:
        lines = compliance_text.split('\n')
        for line in lines:
            if "最终合规判断：" in line:
                result = line.split("最终合规判断：")[-1].strip()
                return result

    # 如果没有找到明确的判断，尝试从文本中推断
    if "不合规" in compliance_text:
        return "不合规"
    elif "合规" in compliance_text:
        return "合规"
    else:
        return "未明确"

def extract_judgment_reason(compliance_text):
    """从合规评估结果中提取判断理由"""
    if not compliance_text:
        return ""

    # 查找判断理由部分
    if "**判断理由：**" in compliance_text:
        # 分割文本，找到判断理由部分
        parts = compliance_text.split("**判断理由：**")
        if len(parts) > 1:
            reason_part = parts[1]

            # 找到判断理由的结束位置（通常是下一个**标记或详细分析开始）
            end_markers = ["**详细分析：**", "**投票结果统计：**", "**最终优化建议：**"]

            for marker in end_markers:
                if marker in reason_part:
                    reason_part = reason_part.split(marker)[0]
                    break

            # 清理文本
            reason = clean_text(reason_part)
            return reason

    # 如果没有找到标准格式的判断理由，尝试提取其他相关信息
    if "判断理由：" in compliance_text:
        parts = compliance_text.split("判断理由：")
        if len(parts) > 1:
            reason_part = parts[1].split('\n')[0]  # 取第一行
            return clean_text(reason_part)

    return ""

def load_json_files(folder_path):
    """加载所有JSON文件并提取数据"""
    data_list = []

    # 获取所有JSON文件
    json_files = glob.glob(os.path.join(folder_path, "*_result.json"))
    json_files.sort()  # 按文件名排序

    print(f"找到 {len(json_files)} 个JSON文件")

    for file_path in json_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 提取原始数据
            original_data = data.get('原始数据', {})

            # 提取需要的字段
            compliance_full_text = data.get('合规评估结果', '')

            row_data = {
                '文件名': data.get('文件名', os.path.basename(file_path)),
                '类别': original_data.get('类别', ''),
                '问题': clean_text(original_data.get('问题', '')),
                '回答': clean_text(original_data.get('回答', '')),
                '优化后的回答': clean_text(original_data.get('优化后的回答', '')),
                '原因': clean_text(original_data.get('原因', '')),
                '是否可用': original_data.get('是否可用', ''),
                '合规评估结果': extract_compliance_result(compliance_full_text),
                'AI判断理由': extract_judgment_reason(compliance_full_text),
                '评估时间': data.get('评估时间', ''),
                '评估状态': data.get('评估状态', ''),
                '分类': original_data.get('分类', '')
            }

            data_list.append(row_data)

        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
            continue

    return data_list

def create_excel_file(data_list, output_file):
    """创建Excel文件"""
    if not data_list:
        print("没有数据可以写入Excel文件")
        return False

    # 创建DataFrame
    df = pd.DataFrame(data_list)

    # 重新排列列的顺序，按照需求规范
    columns_order = ['类别', '问题', '回答', '优化后的回答', '原因', '是否可用', '合规评估结果', 'AI判断理由']

    # 确保所有需要的列都存在
    for col in columns_order:
        if col not in df.columns:
            df[col] = ''

    # 选择并重新排序列
    df_final = df[columns_order].copy()

    # 创建工作簿
    wb = Workbook()
    ws = wb.active
    ws.title = "合规审查汇总"

    # 设置列标题
    headers = ['类别', '问题', '回答', '优化后的回答', '原因', '是否可用', '合规评估结果', 'AI判断理由']

    # 设置边框样式
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # 写入标题行
    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col_num, value=header)
        # 设置标题样式
        cell.font = Font(bold=True, size=12, name='微软雅黑')
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.fill = PatternFill(start_color='D9E1F2', end_color='D9E1F2', fill_type='solid')
        cell.border = thin_border

    # 写入数据
    for row_num, row_data in enumerate(df_final.itertuples(index=False), 2):
        for col_num, value in enumerate(row_data, 1):
            cell = ws.cell(row=row_num, column=col_num, value=str(value) if value else "")

            # 设置数据样式
            cell.alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
            cell.border = thin_border
            cell.font = Font(size=10, name='微软雅黑')

    # 调整列宽
    column_widths = {
        'A': 15,  # 类别
        'B': 40,  # 问题
        'C': 50,  # 回答
        'D': 50,  # 优化后的回答
        'E': 30,  # 原因
        'F': 12,  # 是否可用
        'G': 15,  # 合规评估结果
        'H': 60   # AI判断理由
    }

    for col, width in column_widths.items():
        ws.column_dimensions[col].width = width

    # 设置行高（自动调整）
    for row in ws.iter_rows(min_row=2):
        ws.row_dimensions[row[0].row].height = None  # 自动调整行高

    # 冻结首行
    ws.freeze_panes = 'A2'

    # 保存文件
    try:
        wb.save(output_file)
        print(f"Excel文件已保存到: {output_file}")
        print(f"共处理了 {len(data_list)} 条记录")
        return True
    except Exception as e:
        print(f"保存Excel文件时出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("合规审查汇总Excel生成器")
    print("=" * 50)

    # 设置文件夹路径和输出文件名
    folder_path = "合规审查结果"
    output_file = "合规审查汇总.xlsx"

    print("开始处理合规审查数据...")

    # 检查文件夹是否存在
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹 '{folder_path}' 不存在")
        return

    # 加载JSON数据
    print("正在加载JSON文件...")
    data_list = load_json_files(folder_path)

    if not data_list:
        print("错误: 没有找到有效的JSON文件或数据")
        return

    print(f"成功加载 {len(data_list)} 个文件的数据")

    # 创建Excel文件
    print("正在创建Excel文件...")
    success = create_excel_file(data_list, output_file)

    if success:
        print("=" * 50)
        print("处理完成!")
        print(f"输出文件: {output_file}")
        print(f"共处理了 {len(data_list)} 条记录")
        print("=" * 50)

        # 显示数据统计
        df = pd.DataFrame(data_list)
        print("\n数据统计:")
        print(f"- 总记录数: {len(data_list)}")
        if '类别' in df.columns:
            print("- 按类别分布:")
            category_counts = df['类别'].value_counts()
            for category, count in category_counts.items():
                print(f"  {category}: {count} 条")

        if '合规评估结果' in df.columns:
            print("- 合规评估结果分布:")
            compliance_counts = df['合规评估结果'].value_counts()
            for result, count in compliance_counts.items():
                print(f"  {result}: {count} 条")
    else:
        print("Excel文件创建失败")

if __name__ == "__main__":
    main()
