#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证生成的Excel文件
"""

import pandas as pd
import os

def verify_excel_file(excel_file):
    """验证Excel文件内容"""
    if not os.path.exists(excel_file):
        print(f"错误：文件 {excel_file} 不存在")
        return False
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file, sheet_name='合规审查汇总')
        
        print("=" * 60)
        print(f"Excel文件验证报告: {excel_file}")
        print("=" * 60)
        
        # 基本信息
        print(f"总行数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        print(f"列名: {list(df.columns)}")
        
        print("\n" + "=" * 60)
        print("数据统计")
        print("=" * 60)
        
        # 按类别统计
        if '类别' in df.columns:
            print("\n按类别分布:")
            category_counts = df['类别'].value_counts()
            for category, count in category_counts.items():
                print(f"  {category}: {count} 条")
        
        # 按合规评估结果统计
        if '合规评估结果' in df.columns:
            print("\n按合规评估结果分布:")
            compliance_counts = df['合规评估结果'].value_counts()
            for result, count in compliance_counts.items():
                print(f"  {result}: {count} 条")
        
        # 显示前几行数据样本
        print("\n" + "=" * 60)
        print("数据样本 (前3行)")
        print("=" * 60)

        # 显示主要列，包括新增的列
        main_columns = ['类别', '问题', '回答', '是否可用', '合规评估结果', 'AI判断理由']
        available_columns = [col for col in main_columns if col in df.columns]

        sample_df = df[available_columns].head(3)

        for idx, row in sample_df.iterrows():
            print(f"\n第 {idx + 1} 行:")
            for col in available_columns:
                value = str(row[col])
                if col == 'AI判断理由' and len(value) > 150:
                    value = value[:150] + "..."
                elif len(value) > 80:
                    value = value[:80] + "..."
                print(f"  {col}: {value}")

        # 专门检查新增列的数据
        print("\n" + "=" * 60)
        print("新增列数据检查")
        print("=" * 60)

        if '是否可用' in df.columns:
            print("\n'是否可用'列统计:")
            usage_counts = df['是否可用'].value_counts()
            for usage, count in usage_counts.items():
                print(f"  {usage}: {count} 条")

        if 'AI判断理由' in df.columns:
            print("\n'AI判断理由'列统计:")
            non_empty_reasons = df['AI判断理由'].dropna()
            non_empty_reasons = non_empty_reasons[non_empty_reasons != '']
            print(f"  有判断理由的记录: {len(non_empty_reasons)} 条")
            print(f"  无判断理由的记录: {len(df) - len(non_empty_reasons)} 条")

            if len(non_empty_reasons) > 0:
                print(f"  判断理由示例:")
                sample_reason = str(non_empty_reasons.iloc[0])
                if len(sample_reason) > 200:
                    sample_reason = sample_reason[:200] + "..."
                print(f"    {sample_reason}")
        
        # 检查数据完整性
        print("\n" + "=" * 60)
        print("数据完整性检查")
        print("=" * 60)
        
        for col in df.columns:
            null_count = df[col].isnull().sum()
            empty_count = (df[col] == '').sum()
            total_missing = null_count + empty_count
            print(f"{col}: {total_missing} 个缺失值 (空值: {null_count}, 空字符串: {empty_count})")
        
        return True
        
    except Exception as e:
        print(f"读取Excel文件时出错: {str(e)}")
        return False

def main():
    """主函数"""
    excel_file = "合规审查汇总.xlsx"
    verify_excel_file(excel_file)

if __name__ == "__main__":
    main()
