#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
合规审查汇总最终报告
"""

import pandas as pd
import os
from datetime import datetime

def generate_final_report():
    """生成最终报告"""
    print("=" * 80)
    print("合规审查汇总Excel文件生成完成报告")
    print("=" * 80)
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输出文件
    excel_file = "合规审查汇总.xlsx"
    if not os.path.exists(excel_file):
        print("❌ 错误：Excel文件未找到")
        return
    
    print(f"✅ Excel文件已成功生成: {excel_file}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file, sheet_name='合规审查汇总')
        
        print(f"✅ 文件验证通过")
        print(f"📊 数据概览:")
        print(f"   - 总记录数: {len(df)} 条")
        print(f"   - 总列数: {len(df.columns)} 列")
        print(f"   - 列名: {', '.join(df.columns)}")
        
        # 文件大小
        file_size = os.path.getsize(excel_file)
        file_size_mb = file_size / (1024 * 1024)
        print(f"   - 文件大小: {file_size_mb:.2f} MB")
        
        print("\n" + "=" * 80)
        print("数据分布统计")
        print("=" * 80)
        
        # 按类别统计
        if '类别' in df.columns:
            print("\n📋 按类别分布:")
            category_counts = df['类别'].value_counts()
            for category, count in category_counts.items():
                percentage = (count / len(df)) * 100
                print(f"   {category}: {count} 条 ({percentage:.1f}%)")
        
        # 按合规评估结果统计
        if '合规评估结果' in df.columns:
            print("\n⚖️ 按合规评估结果分布:")
            compliance_counts = df['合规评估结果'].value_counts()
            for result, count in compliance_counts.items():
                percentage = (count / len(df)) * 100
                print(f"   {result}: {count} 条 ({percentage:.1f}%)")
        
        # 数据完整性
        print("\n" + "=" * 80)
        print("数据完整性分析")
        print("=" * 80)
        
        for col in df.columns:
            null_count = df[col].isnull().sum()
            empty_count = (df[col] == '').sum()
            total_missing = null_count + empty_count
            completeness = ((len(df) - total_missing) / len(df)) * 100
            
            if total_missing == 0:
                status = "✅"
            elif completeness >= 50:
                status = "⚠️"
            else:
                status = "❌"
            
            print(f"   {status} {col}: {completeness:.1f}% 完整 ({total_missing} 个缺失值)")
        
        # 新增列的统计
        if '是否可用' in df.columns:
            print("\n📋 按'是否可用'分布:")
            usage_counts = df['是否可用'].value_counts()
            for usage, count in usage_counts.items():
                percentage = (count / len(df)) * 100
                print(f"   {usage}: {count} 条 ({percentage:.1f}%)")

        print("\n" + "=" * 80)
        print("文件结构说明")
        print("=" * 80)
        print("📁 生成的Excel文件包含以下列:")
        print("   1. 类别 - 问答对的业务类别")
        print("   2. 问题 - 客户提出的问题")
        print("   3. 回答 - 原始回答内容")
        print("   4. 优化后的回答 - 经过优化的回答内容")
        print("   5. 原因 - 不可用的原因说明")
        print("   6. 是否可用 - 原始人工判断的可用性")
        print("   7. 合规评估结果 - AI评估的合规性判断")
        print("   8. AI判断理由 - AI模型的详细分析理由")
        
        print("\n" + "=" * 80)
        print("使用建议")
        print("=" * 80)
        print("📝 建议用途:")
        print("   1. 合规风险识别和管理")
        print("   2. 客服回答质量评估")
        print("   3. 业务流程优化参考")
        print("   4. 培训材料制作")
        print("   5. 监管报告准备")
        
        print("\n✅ 所有任务已完成！")
        print(f"📄 最终输出文件: {excel_file}")
        
    except Exception as e:
        print(f"❌ 读取Excel文件时出错: {str(e)}")

def main():
    """主函数"""
    generate_final_report()

if __name__ == "__main__":
    main()
